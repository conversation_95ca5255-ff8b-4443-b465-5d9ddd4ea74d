<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" href="/static/logo.bcac7575.png" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, rgba(52, 211, 153, 0.15) 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;

        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .nav-links {
            margin-top: 15px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 10px;
            padding: 8px 16px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
        }

        .quiz-container {
            padding: 40px;
        }

        .question-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .question-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .question-number {
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            min-width: 40px;
        }

        .question-text {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.6;
        }

        .options {
            margin: 25px 0;
        }

        .option {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .option:hover {
            border-color: #3498db;
            background: #f0f8ff;
        }

        .option input[type="radio"] {
            margin-right: 12px;
            transform: scale(1.2);
            pointer-events: none;
        }

        .option label {
            cursor: pointer;
            font-size: 16px;
            line-height: 1.5;
            flex: 1;
            pointer-events: none;
        }

        .option label[for*="_True"] {
            color: #27ae60;
            font-weight: 600;
        }

        .option label[for*="_False"] {
            color: #e74c3c;
            font-weight: 600;
        }

        .option input[type="radio"]:checked + label {
            font-weight: bold;
        }

        .option:has(input[type="radio"]:checked) {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .explanation {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .explanation-title {
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .explanation-text {
            color: #2c3e50;
            line-height: 1.6;
        }

        .controls {
            text-align: center;
            margin: 40px 0;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
        }

        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(149, 165, 166, 0.4);
        }

        .result {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin: 30px 0;
            display: none;
            box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
        }

        .result.show {
            display: block;
            animation: slideIn 0.6s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        .result h2 {
            font-size: 24px;
            margin-bottom: 15px;
        }

        .result p {
            font-size: 18px;
            line-height: 1.6;
        }

        .progress-bar {
            background: #ecf0f1;
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #3498db, #2ecc71);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .quiz-container {
                padding: 20px;
            }
            
            .question-card {
                padding: 20px;
            }
            
            .question-text {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><img style=" width: 50px; height: 50px;margin-right:10px;" src="/static/logo.bcac7575.png"/>商洛市烟草专卖局{{.title}}答题系统</h1>
            <p>测试您对{{.title}}的掌握程度</p>
            <div class="nav-links">
{{/*                <a href="javascript:void(0)" onclick="showDashboard()">📈 系统仪表板</a>*/}}
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="quiz-container">
            <form id="quizForm">
                <!-- 题目将通过JavaScript动态生成 -->
            </form>

            <div class="controls">
                <button type="button" class="btn" id="submitBtn">提交答案</button>
                <button type="button" class="btn btn-secondary" id="showAnswersBtn" style="display: none;">显示解析</button>
                <button type="button" class="btn btn-secondary" id="resetBtn" style="display: none;">重新答题</button>
            </div>

            <div class="result" id="result">
                <!-- 结果将通过JavaScript显示 -->
            </div>
        </div>
    </div>

    <script>
        // API路径配置 - 兼容本地和线上环境
        const API_BASE_PATH = (() => {
            const hostname = window.location.hostname;
            const port = window.location.port;
            const pathname = window.location.pathname;
            if (pathname.startsWith('/admin/')) {
                return '/admin/api';
            }
            return '/api';
        })();

        // 时间格式化函数 - 处理UTC时间转东八区
        function formatDateTime(dateTime) {
            if (!dateTime) return '';

            let date;
            if (typeof dateTime === 'string') {
                // 数据库返回的时间是UTC时间，但没有Z后缀
                if (dateTime.indexOf('T') === -1) {
                    // 如果是 "2025-08-01 11:58:11" 格式，转换为ISO格式并添加Z
                    date = new Date(dateTime.replace(' ', 'T') + 'Z');
                } else if (dateTime.indexOf('Z') === -1 && dateTime.indexOf('+') === -1) {
                    // 如果是ISO格式但没有时区信息，添加Z表示UTC
                    date = new Date(dateTime + 'Z');
                } else {
                    date = new Date(dateTime);
                }
            } else if (dateTime instanceof Date) {
                date = dateTime;
            } else {
                return '';
            }

            if (isNaN(date.getTime())) {
                return '';
            }

            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Shanghai'
            };

            return date.toLocaleString('zh-CN', options);
        }

        // 题目数据 - 从Go后端动态传递
        const questionsJson = {{.questionsJSON}};

        let currentAnswers = {};
        let isSubmitted = false;
        const questions = JSON.parse(questionsJson).sort((a, b) => {
            // multiple_choice 排在 true_false 前面
            if (a.question_type === 'multiple_choice' && b.question_type === 'true_false') {
                return -1;
            } else if (a.question_type === 'true_false' && b.question_type === 'multiple_choice') {
                return 1;
            }
            return 0;
        });
        // 初始化答题系统
        function initQuiz() {
            const form = document.getElementById('quizForm');
            if (!form) {
                console.error('quizForm element not found');
                return;
            }

            form.innerHTML = '';

            questions.forEach((q, index) => {
                const questionCard = createQuestionCard(q, index);
                form.appendChild(questionCard);
            });

            updateProgress();
        }

        // 创建题目卡片
        function createQuestionCard(question, index) {
            const card = document.createElement('div');
            card.className = 'question-card';

            let optionsHtml = '';

            if (question.question_type === 'multiple_choice') {
                // 选择题选项
                optionsHtml = question.options.map(option => `
                    <div class="option" onclick="selectOption('${question.question_id}', '${option[0]}')">
                        <input type="radio" id="${question.question_id}_${option[0]}"
                               name="${question.question_id}" value="${option[0]}">
                        <label for="${question.question_id}_${option[0]}">${option}</label>
                    </div>
                `).join('');
            } else if (question.question_type === 'true_false') {
                // 判断题选项
                optionsHtml = `
                    <div class="option" onclick="selectOption('${question.question_id}', 'True')">
                        <input type="radio" id="${question.question_id}_True"
                               name="${question.question_id}" value="True">
                        <label for="${question.question_id}_True">✓ 正确</label>
                    </div>
                    <div class="option" onclick="selectOption('${question.question_id}', 'False')">
                        <input type="radio" id="${question.question_id}_False"
                               name="${question.question_id}" value="False">
                        <label for="${question.question_id}_False">✗ 错误</label>
                    </div>
                `;
            }

            card.innerHTML = `
                <div class="question-header">
                    <div class="question-number">${index + 1}</div>
                    <div class="question-text">
                        ${question.question_type === 'true_false' ? '[判断题] ' : '[选择题] '}
                        ${question.question}
                    </div>
                </div>
                <div class="options">
                    ${optionsHtml}
                </div>
                <div class="explanation" id="explanation_${question.question_id}">
                    <div class="explanation-title">答案解析：</div>
                    <div class="explanation-text">${question.explanation}</div>
                </div>
            `;
            return card;
        }

        // 选择选项（点击整行）
        function selectOption(questionId, answer) {
            if (isSubmitted) return; // 如果已提交，不允许修改

            const radioButton = document.getElementById(`${questionId}_${answer}`);
            if (radioButton && !radioButton.disabled) {
                radioButton.checked = true;
                handleAnswerChange(questionId, answer);
            }
        }

        // 处理答案变化
        function handleAnswerChange(questionId, answer) {
            currentAnswers[questionId] = answer;
            updateProgress();
        }

        // 更新进度条
        function updateProgress() {
            const totalQuestions = questions.length;
            const answeredQuestions = Object.keys(currentAnswers).length;
            const progress = (answeredQuestions / totalQuestions) * 100;

            const progressFill = document.getElementById('progressFill');
            if (progressFill) {
                progressFill.style.width = progress + '%';
            }
        }

        // 获取会话信息
        function getSessionInfo() {
            const consoleToken = localStorage.getItem('console_token');
            let sessionInfo = {
                sessionId: 'anonymous_' + Date.now(),
                timestamp: new Date().toISOString()
            };

            if (consoleToken) {
                try {
                    // 解析JWT token获取用户信息
                    const payload = JSON.parse(atob(consoleToken.split('.')[1]));
                    sessionInfo.userId = payload.user_id;
                    sessionInfo.sessionId = 'user_' + payload.user_id + '_' + Date.now();
                    sessionInfo.userType = 'authenticated';
                } catch (e) {
                    console.warn('Failed to parse console_token:', e);
                    sessionInfo.userType = 'anonymous';
                }
            } else {
                sessionInfo.userType = 'anonymous';
            }

            return sessionInfo;
        }

        // 提交答题到服务器
        async function submitQuiz() {
            if (Object.keys(currentAnswers).length < questions.length) {
                alert('请完成所有题目后再提交！');
                return;
            }

            // 显示加载状态
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '提交中...';
            submitBtn.disabled = true;

            try {
                // 获取当前页面的UUID
                const pathParts = window.location.pathname.split('/');
                const quizUUID = pathParts[pathParts.length - 1];

                // 准备提交数据
                const submitData = {
                    answers: currentAnswers,
                    session_info: getSessionInfo()
                };

                // 提交到服务器
                const response = await fetch(`${API_BASE_PATH}/quiz/${quizUUID}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    },
                    body: JSON.stringify(submitData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.result === 'success') {
                    displayResults(result.data);
                } else {
                    throw new Error(result.message || '提交失败');
                }

            } catch (error) {
                console.error('Submit error:', error);
                alert('提交失败：' + error.message);

                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        }

        // 显示答题结果
        function displayResults(data) {
            const resultDiv = document.getElementById('result');

            let resultMessage = '';
            if (data.percentage >= 90) {
                resultMessage = '🎉 优秀！您的表现非常出色！';
            } else if (data.percentage >= 80) {
                resultMessage = '👍 良好！您的表现不错！';
            } else if (data.percentage >= 60) {
                resultMessage = '📚 及格！继续加油！';
            } else {
                resultMessage = '💪 需要加强！建议重新学习相关知识点。';
            }

            resultDiv.innerHTML = `
                <h3>答题结果</h3>
                <div class="score-summary">
                    <p>得分：<strong>${data.score}分 (${data.percentage.toFixed(1)}%)</strong></p>
                    <p>正确题数：<strong>${data.correct_answers}/${data.total_questions}</strong></p>
                    <p>提交时间：<strong>${formatDateTime(data.submitted_at)}</strong></p>
                    <p>${resultMessage}</p>
                </div>
                <div class="analysis">
                    <h4>详细分析</h4>
                    <p>${data.analysis}</p>
                </div>
            `;

            resultDiv.classList.add('show');
            isSubmitted = true;

            // 存储详细结果用于显示解析
            window.detailedResults = data.detailed_results;

            // 显示控制按钮
            document.getElementById('submitBtn').style.display = 'none';
            document.getElementById('showAnswersBtn').style.display = 'inline-block';
            document.getElementById('resetBtn').style.display = 'none'; // 不允许重置重新答题

            // 禁用所有选项
            const inputs = document.querySelectorAll('input[type="radio"]');
            inputs.forEach(input => input.disabled = true);
        }

        // 显示解析
        function showAnswers() {
            if (window.detailedResults) {
                // 使用服务器返回的详细结果
                window.detailedResults.forEach(result => {
                    const explanation = document.getElementById(`explanation_${result.question_id}`);
                    if (explanation) {
                        explanation.classList.add('show');
                        // 更新解析内容为服务器返回的解析
                        explanation.innerHTML = `<strong>解析：</strong>${result.explanation}`;
                    }

                    // 高亮正确答案（绿色）
                    const correctOption = document.querySelector(`input[name="${result.question_id}"][value="${result.correct_answer}"]`);
                    if (correctOption) {
                        correctOption.parentElement.style.background = '#d4edda';
                        correctOption.parentElement.style.borderColor = '#28a745';
                        correctOption.parentElement.style.fontWeight = 'bold';
                        // 添加正确标记
                        if (!correctOption.parentElement.querySelector('.answer-mark')) {
                            const mark = document.createElement('span');
                            mark.className = 'answer-mark';
                            mark.innerHTML = ' ✓ 正确答案';
                            mark.style.color = '#28a745';
                            mark.style.fontWeight = 'bold';
                            mark.style.marginLeft = '10px';
                            correctOption.parentElement.appendChild(mark);
                        }
                    }

                    // 标记用户的错误答案（红色）
                    if (result.user_answer && result.user_answer !== result.correct_answer) {
                        const wrongOption = document.querySelector(`input[name="${result.question_id}"][value="${result.user_answer}"]`);
                        if (wrongOption) {
                            wrongOption.parentElement.style.background = '#f8d7da';
                            wrongOption.parentElement.style.borderColor = '#dc3545';
                            // 添加错误标记
                            if (!wrongOption.parentElement.querySelector('.answer-mark')) {
                                const mark = document.createElement('span');
                                mark.className = 'answer-mark';
                                mark.innerHTML = ' ✗ 您的答案';
                                mark.style.color = '#dc3545';
                                mark.style.fontWeight = 'bold';
                                mark.style.marginLeft = '10px';
                                wrongOption.parentElement.appendChild(mark);
                            }
                        }
                    } else if (result.user_answer === result.correct_answer) {
                        // 用户答案正确的情况，添加正确标记
                        if (correctOption && !correctOption.parentElement.querySelector('.user-correct-mark')) {
                            const mark = document.createElement('span');
                            mark.className = 'user-correct-mark';
                            mark.innerHTML = ' ✓ 您答对了';
                            mark.style.color = '#28a745';
                            mark.style.fontWeight = 'bold';
                            mark.style.marginLeft = '10px';
                            correctOption.parentElement.appendChild(mark);
                        }
                    }
                });
            } else {
                // 回退到原有逻辑
                questions.forEach(q => {
                    const explanation = document.getElementById(`explanation_${q.question_id}`);
                    if (explanation) {
                        explanation.classList.add('show');
                    }

                    // 高亮正确答案
                    const correctOption = document.querySelector(`input[name="${q.question_id}"][value="${q.correct_answer}"]`);
                    if (correctOption) {
                        correctOption.parentElement.style.background = '#d4edda';
                        correctOption.parentElement.style.borderColor = '#28a745';
                    }

                    // 标记错误答案
                    const userAnswer = currentAnswers[q.question_id];
                    if (userAnswer && userAnswer !== q.correct_answer) {
                        const wrongOption = document.querySelector(`input[name="${q.question_id}"][value="${userAnswer}"]`);
                        if (wrongOption) {
                            wrongOption.parentElement.style.background = '#f8d7da';
                            wrongOption.parentElement.style.borderColor = '#dc3545';
                        }
                    }
                });
            }

            const showAnswersBtn = document.getElementById('showAnswersBtn');
            if (showAnswersBtn) {
                showAnswersBtn.style.display = 'none';
            }
        }

        // 重置答题
        function resetQuiz() {
            currentAnswers = {};
            isSubmitted = false;

            // 重置界面
            document.getElementById('result').classList.remove('show');
            document.getElementById('submitBtn').style.display = 'inline-block';
            document.getElementById('showAnswersBtn').style.display = 'none';
            document.getElementById('resetBtn').style.display = 'none';

            // 重新初始化
            initQuiz();
        }

        // 事件监听
        document.getElementById('submitBtn').addEventListener('click', submitQuiz);
        document.getElementById('showAnswersBtn').addEventListener('click', showAnswers);
        document.getElementById('resetBtn').addEventListener('click', resetQuiz);

        // 显示统计信息
        async function showStatistics() {
            try {
                const pathParts = window.location.pathname.split('/');
                const quizUUID = pathParts[pathParts.length - 1];

                const response = await fetch(`${API_BASE_PATH}/quiz/${quizUUID}/statistics`, {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (result.result === 'success') {
                    displayStatistics(result.data);
                } else {
                    throw new Error(result.message || '获取统计信息失败');
                }
            } catch (error) {
                console.error('Statistics error:', error);
                alert('获取统计信息失败：' + error.message);
            }
        }

        // 显示仪表板
        async function showDashboard() {
            try {
                const response = await fetch('${API_BASE_PATH}/quiz/dashboard', {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (result.result === 'success') {
                    displayDashboard(result.data);
                } else {
                    throw new Error(result.message || '获取仪表板数据失败');
                }
            } catch (error) {
                console.error('Dashboard error:', error);
                alert('获取仪表板数据失败：' + error.message);
            }
        }

        // 显示统计信息弹窗
        function displayStatistics(data) {
            const modal = createModal('题目统计信息', `
                <div style="text-align: left;">
                    <h3>${data.quiz_title}</h3>
                    <div style="margin: 20px 0;">
                        <p><strong>总提交数：</strong>${data.total_submissions}</p>
                        <p><strong>平均分：</strong>${data.average_score.toFixed(1)}分</p>
                        <p><strong>最高分：</strong>${data.highest_score}分</p>
                        <p><strong>最低分：</strong>${data.lowest_score}分</p>
                        <p><strong>及格率：</strong>${data.pass_rate.toFixed(1)}%</p>
                    </div>

                    <h4>分数分布：</h4>
                    <div style="margin: 10px 0;">
                        ${Object.entries(data.score_distribution || {}).map(([range, count]) =>
                            `<p>${range}分：${count}人</p>`
                        ).join('')}
                    </div>

                    ${data.recent_submissions && data.recent_submissions.length > 0 ? `
                        <h4>最近提交：</h4>
                        <div style="max-height: 200px; overflow-y: auto;">
                            ${data.recent_submissions.map(sub => `
                                <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;">
                                    <p><strong>得分：</strong>${sub.percentage.toFixed(1)}% (${sub.score}分)</p>
                                    <p><strong>时间：</strong>${sub.submitted_at}</p>
                                    ${sub.user_info && sub.user_info.name ? `<p><strong>用户：</strong>${sub.user_info.name}</p>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `);
        }

        // 显示仪表板弹窗
        function displayDashboard(data) {
            const modal = createModal('系统仪表板', `
                <div style="text-align: left;">
                    <div style="margin: 20px 0;">
                        <h3>系统概览</h3>
                        <p><strong>总批次数：</strong>${data.total_batches}</p>
                        <p><strong>总题目数：</strong>${data.total_quizzes}</p>
                        <p><strong>总提交数：</strong>${data.total_submissions}</p>
                    </div>

                    ${data.batch_statistics && data.batch_statistics.length > 0 ? `
                        <h4>批次统计：</h4>
                        <div style="max-height: 400px; overflow-y: auto;">
                            ${data.batch_statistics.map(batch => `
                                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
                                    <h5>${batch.batch_title}</h5>
                                    <p><strong>题目数：</strong>${batch.total_quizzes}</p>
                                    <p><strong>提交数：</strong>${batch.total_submissions}</p>
                                    <p><strong>平均分：</strong>${batch.average_score.toFixed(1)}分</p>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `);
        }

        // 创建模态框
        function createModal(title, content) {
            // 移除已存在的模态框
            const existingModal = document.getElementById('statisticsModal');
            if (existingModal) {
                existingModal.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'statisticsModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    max-width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                    position: relative;
                ">
                    <h2 style="margin-bottom: 20px; text-align: center;">${title}</h2>
                    ${content}
                    <button onclick="document.getElementById('statisticsModal').remove()"
                            style="
                                position: absolute;
                                top: 10px;
                                right: 15px;
                                background: none;
                                border: none;
                                font-size: 24px;
                                cursor: pointer;
                                color: #999;
                            ">×</button>
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="document.getElementById('statisticsModal').remove()"
                                style="
                                    background: #007bff;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 5px;
                                    cursor: pointer;
                                ">关闭</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            return modal;
        }

        // 解析功能已移除

        // 检查登录状态
        async function checkLoginStatus() {
            const token = localStorage.getItem('console_token');
            if (!token) {
                showLoginRequired('请先登录后再进行答题');
                return false;
            }

            try {
                // 向服务器验证token是否有效
                const response = await fetch(`${API_BASE_PATH}/verify-login`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.result === 'success') {
                        // 登录状态有效，存储用户信息
                        window.currentUser = result.data;
                        return true;
                    }
                }

                // token无效或过期
                // localStorage.removeItem('console_token');
                showLoginRequired('登录状态已过期，请重新登录');
                return false;

            } catch (error) {
                console.error('验证登录状态失败:', error);
                showLoginRequired('网络错误，请检查网络连接后重试');
                return false;
            }
        }

        // 显示登录要求页面
        function showLoginRequired(message) {
            document.body.innerHTML = `
                <div style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                ">
                    <div style="
                        background: white;
                        padding: 40px;
                        border-radius: 15px;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                        text-align: center;
                        max-width: 400px;
                    ">
                        <h2 style="color: #333; margin-bottom: 20px;">🔐 需要登录</h2>
                        <p style="color: #666; margin-bottom: 30px;">${message}</p>
                        <button onclick="window.location.href='/'" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 12px 30px;
                            border-radius: 25px;
                            cursor: pointer;
                            font-size: 16px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'">
                            前往登录
                        </button>
                    </div>
                </div>
            `;
        }

        // 显示验证状态（非侵入式）
        function showVerifyingStatus() {
            const header = document.querySelector('.header');
            if (header) {
                const verifyDiv = document.createElement('div');
                verifyDiv.id = 'verifying-status';
                verifyDiv.style.cssText = `
                    background: rgba(255, 255, 255, 0.9);
                    padding: 10px;
                    text-align: center;
                    border-radius: 5px;
                    margin-top: 10px;
                `;
                verifyDiv.innerHTML = `
                    <div style="display: inline-block; margin-right: 10px;">
                        <div style="
                            width: 20px;
                            height: 20px;
                            border: 2px solid #f3f3f3;
                            border-top: 2px solid #007bff;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            display: inline-block;
                        "></div>
                    </div>
                    <span style="color: #333;">验证登录状态...</span>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                `;
                header.appendChild(verifyDiv);
            }
        }

        // 隐藏验证状态
        function hideVerifyingStatus() {
            const verifyDiv = document.getElementById('verifying-status');
            if (verifyDiv) {
                verifyDiv.remove();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 确保DOM元素存在
            const quizForm = document.getElementById('quizForm');
            if (!quizForm) {
                console.error('答题页面DOM元素未找到，可能不在答题页面');
                return;
            }

            // 首先检查登录状态
            const isLoggedIn = await checkLoginStatus();
            if (!isLoggedIn) {
                return;
            }

            // 检查用户是否已经答过题
            checkUserAnsweredStatus();
        });

        // 检查用户答题状态
        async function checkUserAnsweredStatus() {
            try {
                const pathParts = window.location.pathname.split('/');
                const quizUUID = pathParts[pathParts.length - 1];

                const response = await fetch(`${API_BASE_PATH}/quiz/${quizUUID}/check`, {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.result === 'success' && result.data.has_answered) {
                        // 用户已答题，先初始化页面再显示结果
                        initQuiz();
                        // 等待DOM更新后再显示完成页面
                        setTimeout(() => {
                            showCompletedQuizPage(result.data);
                        }, 100);
                        return;
                    }
                }

                // 用户未答题，正常初始化答题页面
                initQuiz();

            } catch (error) {
                console.error('Check answered status error:', error);
                // 出错时仍然允许答题
                initQuiz();
            }
        }

        // 显示已完成答题的页面
        function showCompletedQuizPage(data) {
            // 填入用户之前的答案
            if (data.user_answers) {
                fillUserAnswers(data.user_answers);
            }

            // 显示结果
            const resultDiv = document.getElementById('result');
            if (resultDiv) {
                resultDiv.innerHTML = `
                    <h3>您已完成答题 - 查看解析</h3>
                    <div class="score-summary">
                        <p>您的得分：<strong>${data.latest_score}分 (${data.latest_percentage.toFixed(1)}%)</strong></p>
                        <p>答题时间：<strong>${formatDateTime(data.submitted_at)}</strong></p>
                        <p style="color: #666; margin-top: 15px;">以下是您的答题结果和解析</p>
                    </div>
                `;
                resultDiv.classList.add('show');
            }

            // 存储详细结果用于显示解析
            if (data.detailed_results) {
                window.detailedResults = data.detailed_results;
                // 自动显示解析
                showAnswers();
            }

            // 隐藏提交按钮和解析按钮（因为已经自动显示了）
            const submitBtn = document.getElementById('submitBtn');
            const showAnswersBtn = document.getElementById('showAnswersBtn');
            const resetBtn = document.getElementById('resetBtn');

            if (submitBtn) submitBtn.style.display = 'none';
            if (showAnswersBtn) showAnswersBtn.style.display = 'none'; // 已经自动显示解析了
            if (resetBtn) resetBtn.style.display = 'none';

            // 禁用所有选项
            const inputs = document.querySelectorAll('input[type="radio"]');
            inputs.forEach(input => input.disabled = true);

            // 标记为已提交状态
            isSubmitted = true;
        }

        // 填入用户之前的答案
        function fillUserAnswers(userAnswers) {
            if (typeof userAnswers === 'object' && userAnswers !== null) {
                for (const [questionId, answer] of Object.entries(userAnswers)) {
                    const input = document.querySelector(`input[name="${questionId}"][value="${answer}"]`);
                    if (input) {
                        input.checked = true;
                        // 更新当前答案记录
                        currentAnswers[questionId] = answer;
                    }
                }

                // 更新进度条
                updateProgress();
            }
        }

        // 用户答题结果加载功能已移除
    </script>
</body>
</html>

<template>
  <div class="layout-header">
    <span>我的应用</span>
  </div>
  <el-card shadow="never" class="filterCard">
    <div class="filters">
      <el-tag size="large" :type="!currentMode ? 'success' : ''" @click="filterApp('')">全部</el-tag>
      <el-tag size="large" :type="currentMode==='chat' ? 'success' : ''" @click="filterApp('chat')"><img
          src="/images/chat.png"/><span class="textInfo">聊天助手</span></el-tag>
      <el-tag size="large" :type="currentMode==='completion' ? 'success' : ''" @click="filterApp('completion')"><img
          src="/images/completion.png"/><span class="textInfo">文本生成</span></el-tag>
      <el-tag size="large" :type="currentMode==='advanced-chat' ? 'success' : ''" @click="filterApp('advanced-chat')">
        <img
            src="/images/advanced-chat.png"/><span class="textInfo">ChatFlow</span></el-tag>
      <el-tag size="large" :type="currentMode==='workflow' ? 'success' : ''" @click="filterApp('workflow')"><img
          src="/images/workflow.png"/><span class="textInfo">工作流</span></el-tag>
      <el-tag size="large" :type="currentMode==='agent-chat' ? 'success' : ''" @click="filterApp('agent-chat')"><img
          src="/images/agent-chat.png"/><span class="textInfo">Agent</span></el-tag>
      <el-input clearable style="width: 200px;" placeholder="搜索" @input="getData2" v-model="appName"
                :prefix-icon="Search"></el-input>
    </div>
  </el-card>
  <div class="scroll-wrapper" ref="scrollContainer">
    <el-row :gutter="20">
      <el-col :sm="12" :md="8" :lg="8" v-for="item in displayAgents" :key="item.id">
        <div class="modelf" @click="goto(item)">
          <div class="model">
            <div class="newImg">
              <img v-if="item.icon_type!=='emoji'" :src="item.icon_url" alt=""/>
              <div v-else>
                <div class="emoji" :style="{background:item.icon_background}">{{ item.icon }}</div>
              </div>
              <img :src="`/images/${item.mode}.png`" alt="" class="tag">
            </div>
            <div class="info2">
              <span :title="item.name">{{ item.name }}</span>
              <span :title="item.creator_name" v-if="item.mode === 'chat'">聊天助手</span>
              <span :title="item.creator_name" v-else-if="item.mode === 'agent-chat'">Agent</span>
              <span :title="item.creator_name" v-else-if="item.mode === 'completion'">文本生成</span>
              <span :title="item.creator_name" v-else-if="item.mode === 'advanced-chat'">Chatflow</span>
              <span :title="item.creator_name" v-else-if="item.mode === 'workflow'">工作流</span>
              <span class="info" :title="item.description">{{ item.description }}</span>
            </div>
          </div>
          <div class="info3" @click.stop>
            <div class="countInfo">
              <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="点击量"
                  placement="top-start"
              >
                <img src="@/assets/img/svg/fire.svg" alt="" class="w-15"/>
              </el-tooltip>
              <span>{{ item.click_count }}</span>
              <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="使用人次"
                  placement="top-start"
              >
                <img src="@/assets/img/svg/audience_insight.svg" alt="" class="w-15 ml-1 mr-1"/>
              </el-tooltip>
              <span>{{ item.user_count }}</span>
            </div>
            <div class="action-menu">
              <el-dropdown trigger="click">
                <el-icon class="more">
                  <MoreFilled/>
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click.stop="editAgent(item)">编辑</el-dropdown-item>
                    <el-dropdown-item @click.stop="delAgent(item)">删除</el-dropdown-item>
                    <el-dropdown-item @click.stop="copyAgent(item)">复制</el-dropdown-item>
                    <el-dropdown-item @click.stop="exportAgent(item)">导出 DSL</el-dropdown-item>
                    <el-dropdown-item divided>
                      <el-switch
                          @click="updatePublic(item)"
                          v-model="item.only_me"
                          size="small"
                          inline-prompt
                          active-text="公开"
                          inactive-text="私有"
                      />
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div v-if="loading" class="loading-indicator">加载中...</div>
    <div v-if="!loading && agents.length === 0" class="empty">
      <el-empty>
        点击左侧
        <el-icon>
          <CirclePlus/>
        </el-icon>
        创建应用
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, onBeforeUnmount, computed} from 'vue';
import {useRouter} from 'vue-router';
import {ElMessage, ElMessageBox} from 'element-plus';
import {getMyAgentsApi, deleteAgentApi, getAgentApi, setSiteStatus, copyAgentFuc, exportAgentFuc} from '@/api/agent';
import {saveClick, getAppClicks, getAppStatus} from '@/api/system';
import {MoreFilled, CirclePlus, Search} from '@element-plus/icons-vue';
import $moment from 'moment';

const router = useRouter();

const agents = ref([]);
const page = ref(1);
const hasMore = ref(true);
const loading = ref(false);
const totalPages = ref(1);

const scrollContainer = ref<HTMLElement | null>(null);

const getData = async () => {
  console.log('来了aaaa')
  console.log(loading.value, !hasMore.value)
  if (loading.value || !hasMore.value) return;

  loading.value = true;
  let res1 = await getAppStatus();
  let res2 = await getAppClicks();
  try {
    const res = await getMyAgentsApi({
      page: page.value,
      is_created_by_me: true,
    });
    const statusMap = Object.fromEntries(
        res1.data.map(({app_id, only_me}) => [app_id, only_me])
    );
    const merged = [...agents.value, ...res.data];

// 再统一做字段补充 / 映射
    agents.value = merged.map(app => ({
      ...app,
      only_me: statusMap[app.id] ?? false,
      click_count: res2.data[app.id]?.total_count ?? 0,
      user_count: res2.data[app.id]?.user_count ?? 0
    }));

    // 用后端返回的 has_more 字段判断是否还有更多数据
    if (res.has_more === false) {
      hasMore.value = false;
    } else {
      page.value++;
    }
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};
const appName = ref('');
const getData2 = async () => {
  loading.value = true;
  let res1 = await getAppStatus();
  let res2 = await getAppClicks();
  try {
    const res = await getMyAgentsApi({
      page: 1,
      limit: 100,
      name: appName.value,
      is_created_by_me: true,
    });
    const statusMap = Object.fromEntries(
        res1.data.map(({app_id, only_me}) => [app_id, only_me])
    );
    const merged = [...res.data];

// 再统一做字段补充 / 映射
    agents.value = merged.map(app => ({
      ...app,
      only_me: statusMap[app.id] ?? false,
      click_count: res2.data[app.id]?.total_count ?? 0,
      user_count: res2.data[app.id]?.user_count ?? 0
    }));
    agents.value = agents.value.filter(item => item.only_me);
    console.log('agents.value', agents.value)
    // 用后端返回的 has_more 字段判断是否还有更多数据
    if (res.has_more === false) {
      console.log('没了')
      hasMore.value = false;
    } else {
      console.log('还有')
      page.value++;
    }
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};
const handleScroll = () => {
  if (!scrollContainer.value) return;
  const container = scrollContainer.value;
  const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;
  console.log('scrollBottom', scrollBottom)
  if (scrollBottom < 20) {
    getData();
  }
};

onMounted(() => {
  getData();
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', handleScroll);
  }
});

onBeforeUnmount(() => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll);
  }
});

const goto = async (item) => {

  if (!localStorage.getItem('console_token')) {
    router.push('/login');
    return;
  }
  await saveClick(item.id);
  let res = await getAgentApi(item.id);
  router.push(`/aimain/${res.site.access_token}?type=${item.mode}`);
};

const editAgent = (agent) => {
  if (agent.mode === 'workflow' || agent.mode === 'advanced-chat') {
    router.push('/flowSetting?id=' + agent.id);
  } else {
    router.push('/botSetting?id=' + agent.id);
  }
};

const delAgent = async (agent) => {
  ElMessageBox.confirm(
      `确定要删除应用 "${agent.name}" 吗？此操作无法恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(async () => {
        try {
          await deleteAgentApi(agent.id);
          // 删除后重置分页，重新加载第一页
          page.value = 1;
          agents.value = [];
          totalPages.value = 1;
          hasMore.value = true;
          await getData();
        } catch (err) {
          ElMessage.error('删除失败，请稍后重试');
        }
      })
      .catch(() => {
        ElMessage.info('已取消删除');
      });
};

const copyAgent = async (agent) => {
  console.log('agent', agent)
  ElMessageBox.confirm(
      `确定要复制应用 "${agent.name}" 吗？`,
      '复制确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(async () => {
        try {
          let data = {};
          if (agent.icon_type === 'image') {
            data = {
              icon: agent.icon,
              icon_type: agent.icon_type,
              mode: agent.mode,
              name: agent.name,
            }
          } else {
            data = {
              icon: agent.icon,
              icon_background: agent.icon_background,
              icon_type: agent.icon_type,
              mode: agent.mode,
              name: agent.name,
            }
          }
          await copyAgentFuc(agent.id, data);
          page.value = 1;
          agents.value = [];
          totalPages.value = 1;
          hasMore.value = true;
          await getData();
        } catch (err) {
          ElMessage.error('删除失败，请稍后重试');
        }
      })
      .catch(() => {
        ElMessage.info('已取消删除');
      });
};
const exportAgent = async (agent) => {
  try {
    // 调用接口，拿到文件内容
    const fileContent = await exportAgentFuc(agent.id);

    // 这里假设返回的是 YAML 文件内容
    const blob = new Blob([fileContent.data], { type: 'text/yaml;charset=utf-8' });

    // 创建一个下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'agent.yml'; // 你要保存的文件名
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  } catch (err) {
    console.error('导出失败：', err);
  }
}
const updatePublic = async (item) => {
  await setSiteStatus({
    only_me: item.only_me,
    app_id: item.id,
  });
}
// const nowApp = ref({});
// const getAppInfo = async (app)=>{
//   nowApp.value = await getAgentApi(app.id);
// }

const currentMode = ref('');

/** ② 点击标签时更新 currentMode */
function filterApp(mode: string) {
  currentMode.value = mode;
}

const displayAgents = computed(() => {
  if (!currentMode.value) return agents.value;          // 不过滤
  return agents.value.filter(a => a.mode === currentMode.value);
});
</script>

<style scoped lang="less">
.layout-header {
  font-size: 18px;
  font-weight: bold;
}

.scroll-wrapper {
  height: calc(100vh - 100px);
  overflow-y: auto;
  padding-right: 10px;
}

.modelf {
  margin-bottom: 20px;
  border-radius: 10px;
  padding: 20px 20px 16px;
  border-color: rgba(82, 100, 154, 0.13);
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  cursor: pointer;
  background-color: #fff;
}

.model {
  display: flex;
  position: relative;

  .emoji {
    width: 57px;
    overflow: hidden;
    font-size: 42px;
    border-radius: 14px;
    padding-bottom: 2px;
  }

  &:hover {
    .editIcon {
      display: block !important;
    }
  }

  .editIcon {
    display: none;
    position: absolute;
    right: 30px;
    top: 0px;

    &:hover {
      background-color: #eff0f8;
    }
  }

  .editIcon2 {
    display: none;
    position: absolute;
    right: 0px;
    top: 0px;

    &:hover {
      background-color: #eff0f8;
    }
  }
}

.model img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
}

.model span {
  color: #333333;
  font-size: 14px;
}

.model span:first-child {
  font-size: 18px;
}

.model span:last-child {
  color: rgba(56, 55, 67, 0.8);
}

.model span:nth-child(2) {
  color: rgba(56, 55, 67, 0.8);
  margin-bottom: 5px;
}

.modelf:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08), 0 8px 24px 0 rgba(0, 0, 0, 0.04);
  border-color: rgba(82, 100, 154, 0.13);
}

.info {
  height: 40px;
}

.info2 {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
}

.info2 > :last-child {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info2 > :first-child {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  margin-bottom: 10px;
  text-overflow: ellipsis;
}

.info3 {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgb(240, 240, 245);
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: gray;
  font-size: 12px;
}

.info3 i {
  margin-top: 3px;
}

.info3 span {
  margin-left: 3px;
}

.info3 > div > :nth-child(2n + 1) {
  margin-left: 10px;
}

.empty {
  height: calc(100% - 95);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #909399;
  text-align: center;

  :deep(.el-empty__bottom) {
    display: flex;
    align-items: center;
  }
}

.more {
  padding: 8px;
  cursor: pointer;
  font-size: 16px !important;
}

.more:hover {
  background-color: rgba(87, 104, 161, 0.08);
  border-radius: 8px;
}

.loading-indicator {
  text-align: center;
  padding: 10px 0;
  color: #999;
}

.newImg {
  position: relative;

  .tag {
    position: absolute;
    right: 0;
    top: 50px;
    width: 20px;
    height: 20px;
  }
}

.filters {
  > span {
    margin-right: 10px;
    cursor: pointer;
    font-size: 20px;
  }

  img {
    width: 20px;
  }

  .textInfo {
    margin-left: 5px;
    vertical-align: 2px;
  }
}

.filterCard {
  margin-bottom: 10px;
  position: sticky;
  top: 0;
  left: 0;
  background-color: #fff;
  z-index: 999;
}

.countInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 60px;
}
</style>

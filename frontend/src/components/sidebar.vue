<template>
  <div class="sidebar">
    <div class="pcname">
      <transition name="fade" mode="out-in">
        <div key="full" class="full-name">
          商洛市烟草专卖局(公司)<br/>AI平台
        </div>
      </transition>
    </div>
    <el-dropdown trigger="click" class="workspaces">
      <!-- 触发器：左边头像 + 名称 + 下拉箭头 -->
      <span class="flex items-center cursor-pointer">
      <el-avatar size="small" class="mr-1 bg-primary text-white avatar">{{
          workspaceStore?.current?.name?.charAt(0)?.toUpperCase()
        }}</el-avatar>
      <span class="spacename ellipsis">{{ workspaceStore?.current?.name }}</span>
      <el-icon class="ml-1"><ArrowDown/></el-icon>
    </span>

      <!-- 下拉面板 -->
      <template #dropdown>
        <el-dropdown-menu style="width: 220px">
          <el-dropdown-item disabled class="opacity-60">团队</el-dropdown-item>

          <el-dropdown-item
              v-for="item in workspaceStore?.list"
              :key="item?.id"
              @click="choose(item)"
              :class="{ 'is-active': workspaceStore?.current?.id === item?.id }"
          >
            <el-avatar
                size="small"
                class="mr-2"
                style="background:rgb(0, 0, 238);color:#fff"
            >
              {{ item?.name?.charAt(0)?.toUpperCase() }}
            </el-avatar>
            {{ item?.name }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-menu
        class="sidebar-el-menu"
        :default-active="onRoutes"
        :collapse="false"
        :background-color="sidebar.bgColor"
        :text-color="sidebar.textColor"
        router
    >
      <template v-for="item in menuData">
        <template v-if="item.Children.length>0">
          <el-sub-menu :index="item.index" :key="item.index">
            <template #title>
              <el-icon>
                <component :is="item.icon"></component>
              </el-icon>
              <span>{{ item.title }}</span>
            </template>
            <template v-for="subItem in item.Children">
              <el-sub-menu
                  v-if="subItem.Children.length>0"
                  :index="subItem.index"
                  :key="subItem.index"
              >
                <template #title>{{ subItem.title }}</template>
                <el-menu-item
                    v-for="(threeItem, i) in subItem.Children"
                    :key="i"
                    :index="threeItem.index"
                >
                  {{ threeItem.title }}
                </el-menu-item>
              </el-sub-menu>
              <el-menu-item v-else :index="subItem.index">
                {{ subItem.title }}
              </el-menu-item>
            </template>
          </el-sub-menu>
        </template>
        <template v-else>
          <el-menu-item :index="item.index" :key="item.index">
            <el-icon>
              <component :is="item.icon"></component>
            </el-icon>
            <template #title>{{ item.title }}</template>
          </el-menu-item>
        </template>
      </template>
    </el-menu>
    <div class="shuiyin">
      本平台仅面向内部员工开放，用于学习与交流。使用人员须严格遵守保密相关规定，严禁复制、粘贴、下载平台内容，并将其发布至互联网等外部媒体平台
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted} from 'vue';
import {useSidebarStore} from '../store/sidebar';
import {useRoute} from 'vue-router';
import {getMenusApi} from '@/api/system';
import {switchWorkspaces} from '@/api/workspace';
import {Workspace} from "@/types/workspace";
import {useWorkspaceStore} from '@/store/workspace'

const menuData = ref([]);
getMenusApi().then((res: any) => {
  menuData.value = res.data;
});
const route = useRoute();
const onRoutes = computed(() => {
  return route.path;
});

const sidebar = useSidebarStore();
const workspaceStore = useWorkspaceStore()

const choose = async (item: Workspace) => {
  workspaceStore.setCurrent(item.id);
  await switchWorkspaces(item.id);
  location.reload();
}

</script>

<style scoped>
.sidebar {
  display: block;
  position: absolute;
  left: 82px;
  top: 0;
  bottom: 0;
  margin: 10px 0;
  overflow-y: auto;
  border: 1px solid rgba(82, 100, 154, 0.13);
  border-top-right-radius: 14px;
  border-bottom-right-radius: 14px;
}

.sidebar::-webkit-scrollbar {
  width: 0;
}

.sidebar-el-menu:not(.el-menu--collapse) {
  width: 190px;
}

.sidebar-el-menu {
  height: calc(100vh - 120px);
}

.pcname {
  width: 190px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--el-menu-text-color);
  background-color: var(--el-menu-bg-color);
  text-align: center;
  transition: all 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;
}

.pcname.collapsed {
  width: 64px; /* 与Element菜单收起宽度一致 */
}

.full-name {
  font-size: 14px;
  padding: 10px;
  white-space: normal;
  word-break: break-word;
}

.short-name {
  font-size: 20px;
  font-weight: bold;
  margin-left: -105px;
}

.sidebar-el-menu {
  flex: 1;
  overflow-y: auto;
}

.sidebar-el-menu:not(.el-menu--collapse) {
  width: 190px;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.shuiyin {
  position: absolute;
  width: 80%;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
}

.is-active {
  background: var(--el-color-primary-light-9);
}

.bg-primary {
  background: rgb(0, 0, 238);
}

.mr-2 {
  margin-right: 5px;
}

.mr-1 {
  margin-right: 2px;
}

.workspaces {
  overflow: hidden;
  min-width: 70%;
  padding: 0 13px;
  margin-bottom: 20px;
  cursor: pointer;
  /*display: flex;*/
  /*align-items: center;*/
}

.avatar,
.arrow {
  flex-shrink: 0;
}

.spacename {
  flex: 1; /* 把“剩下的”都给它 */
  min-width: 0; /* 关键：允许比内容本身更窄 */
  margin: 0 4px; /* 你的 mr-1 / ml-1 可写在这 */
}
</style>

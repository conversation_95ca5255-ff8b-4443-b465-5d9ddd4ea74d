<template>
  <div class="header">
    <!-- 折叠按钮 -->
    <div class="header-left">
      <img class="logo" src="../assets/img/logo.png" alt=""/>
      <!--      <div class="collapse-btn" @click="collapseChage">-->
      <!--        <el-icon v-if="sidebar.collapse">-->
      <!--          <Expand/>-->
      <!--        </el-icon>-->
      <!--        <el-icon v-else>-->
      <!--          <Fold/>-->
      <!--        </el-icon>-->
      <!--      </div>-->
      <div class="btnIcon" @click="createAppModel = true" v-if="userStore.userRole !== 'normal'">
        <el-icon>
          <CirclePlus/>
        </el-icon>
      </div>
      <div class="line"></div>
      <router-link class="btnIcon" to="/">
        <el-icon>
          <HomeFilled/>
        </el-icon>
      </router-link>
      <div class="btnIcon" @click="showStatistic" v-if="role === 'admin' || role === 'owner'">
        <el-tooltip effect="dark" content="平台数据" placement="top">
          <el-icon>
            <DataLine/>
          </el-icon>
        </el-tooltip>

      </div>

    </div>
    <div class="header-right">
      <div class="header-user-con">
        <div class="btn-icon" @click="router.push('/explanation')">
          <el-tooltip effect="dark" content="使用文档" placement="top">
            <el-icon>
              <Document/>
            </el-icon>
          </el-tooltip>
        </div>
        <div class="btn-icon" @click="router.push('/theme')">
          <el-tooltip effect="dark" content="设置主题" placement="top">
            <i class="el-icon-lx-skin"></i>
          </el-tooltip>
        </div>
        <div class="btn-icon" @click="setFullScreen">
          <el-tooltip effect="dark" content="全屏" placement="top">
            <i class="el-icon-lx-full"></i>
          </el-tooltip>
        </div>
        <!-- 用户头像 -->

        <!-- 用户名下拉菜单 -->
        <el-dropdown class="user-name" placement="right" trigger="click" @command="handleCommand">
            <span class="el-dropdown-link">
              <el-avatar
                  class="user-avatar"
                  :size="32"
                  :src="userAvatar"
                  :style="!userAvatar ? { backgroundColor: '#006CFF', color: '#fff', fontSize: '22px' } : {}"
              >
            <template v-if="!userAvatar">
              {{ me?.name?.charAt(0) || '无' }}
            </template>
          </el-avatar>
            </span>
          <template #dropdown>
            <el-dropdown-menu>
              <!--              <a href="https://github.com/lin-xin/vue-manage-system" target="_blank">-->
              <!--                <el-dropdown-item>项目仓库</el-dropdown-item>-->
              <!--              </a>-->
              <!--              <a href="https://lin-xin.gitee.io/example/vuems-doc/" target="_blank">-->
              <!--                <el-dropdown-item>官方文档</el-dropdown-item>-->
              <!--              </a>-->
              <el-dropdown-item command="user">个人中心</el-dropdown-item>
              <el-dropdown-item divided command="loginout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <el-dialog v-model="createAppModel" title="创建应用" width="1000">
      <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-position="top"
          class="space-y-6 create-app-form"
      >
        <!-- 选择应用类型 -->
        <section>
          <h3 class="section-title">选择应用类型</h3>

          <!-- 新手场景 -->
          <h3 class="group-title">新手场景</h3>
          <el-row :gutter="16" class="mb-6" justify="start">
            <el-col
                v-for="item in basicTemplates"
                :key="item.value"
                :span="6"
                class="mb-4"
            >
              <el-card
                  :body-style="{ padding: '20px', cursor: 'pointer' }"
                  :class="['template-card', { active: ruleForm.mode === item.value }]"
                  @click="selectType(item.value)"
              >
                <el-space direction="vertical" alignment="flex-start">
                  <el-image :src="`/images/${item.value}.png`" class="avatar"/>
                  <span class="font-semibold leading-tight">{{ item.label }}</span>
                  <p class="desc">{{ item.desc }}</p>
                </el-space>
              </el-card>
            </el-col>
          </el-row>

          <!-- 进阶场景 -->
          <h3 class="group-title">进阶场景</h3>
          <el-row :gutter="16" justify="start">
            <el-col
                v-for="item in advancedTemplates"
                :key="item.value"
                :span="6"
                class="mb-4"
            >
              <el-card
                  :body-style="{ padding: '20px', cursor: 'pointer' }"
                  :class="['template-card', { active: ruleForm.mode === item.value }]"
                  @click="selectType(item.value)"
              >
                <el-space direction="vertical" alignment="flex-start">
                  <el-image :src="`/images/${item.value}.png`" class="avatar"/>
                  <span class="font-semibold leading-tight">{{ item.label }}</span>
                  <p class="desc">{{ item.desc }}</p>
                </el-space>
              </el-card>
            </el-col>
            <el-col
                :span="6"
                class="mb-4"
            >
              <el-card
                  :body-style="{ padding: '20px', cursor: 'pointer' }"
                  class="template-card"
                  style="height:auto"
                  @click="importAgentByDsl"
              >
                导入 DSL 文件
              </el-card>
            </el-col>
          </el-row>
        </section>
        <el-divider/>
        <!-- 应用名称 & 图标：左右排列 -->
        <section>
          <el-row :gutter="16" align="top">
            <el-col :span="18">
              <el-form-item prop="name">
                <template #label>
                  <span class="font-medium">应用名称 & 图标</span>
                </template>
                <el-input v-model="ruleForm.name" placeholder="请输入应用名称"/>
              </el-form-item>
              <el-form-item label="描述 (可选)" prop="desc">
                <el-input
                    type="textarea"
                    v-model="ruleForm.description"
                    :rows="3"
                    maxlength="120"
                    show-word-limit
                    placeholder="请输入描述"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6" class="flex justify-center">
              <el-radio-group v-model="ruleForm.icon_type" class="icon_type">
                <el-radio-button label="emoji">表情图标</el-radio-button>
                <el-radio-button label="image">上传图片</el-radio-button>

              </el-radio-group>
              <el-form-item v-if="ruleForm.icon_type === 'emoji'" label="选择表情" prop="icon">
                <el-popover placement="bottom" width="300" trigger="click">
                  <template #reference>
                    <div class="showIcon">
                      <span>{{ ruleForm.icon }}</span>
                    </div>
                  </template>
                  <div class="emoji-grid">
              <span v-for="emoji in emojiList" :key="emoji" @click="ruleForm.icon = emoji">
                {{ emoji }}
              </span>
                  </div>
                </el-popover>
              </el-form-item>

              <!-- 上传模式 -->
              <el-form-item v-else label="图标" prop="iconImage">
                <el-upload
                    class="avatar-uploader"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeUpload"
                    :on-error="handleUploadError"
                >
                  <template v-if="!ruleForm.iconImage && ruleForm.icon_type === 'image'">
                    <img src="@/assets/img/index/watiUpImg.png" class="watiUpImg"/>
                  </template>
                  <template v-else>
                    <img :src="ruleForm.iconImage" class="watiUpImg"/>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <!-- 底部操作按钮 -->
        <div class="flex justify-end gap-3 mt-6">
          <el-button @click="createAppModel = false">取消</el-button>
          <el-button type="primary" @click="toCreateAgent(ruleFormRef)">
            确认
          </el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog
        v-model="showStatisticDialog"
        title="平台数据统计"
        fullscreen
        destroy-on-close
    >
      <template #default>
        <!-- 头部：预设区间 + 自定义日期 -->
        <div class="monitor-header flex items-center mb-4">
          <el-select v-model="monitorPreset" style="width: 160px" @change="applyMonitorPreset">
            <el-option v-for="p in monitorPresets" :key="p.value" :label="p.label" :value="p.value"/>
          </el-select>
          <el-date-picker
              v-model="monitorRange"
              type="daterange"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="margin-left: 12px"
              @change="fetchMonitorStats"
          />
        </div>

        <!-- 总览卡片 -->
        <el-row :gutter="16" class="mb-4">
          <el-col :span="6">
            <el-card shadow="hover" class="box-shadow">
              <div class="stat-title">APP总数</div>
              <div class="stat-value text-2xl font-bold orange"><span class="neon-text">{{ total_apps }}</span></div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="box-shadow">
              <div class="stat-title">用户总数</div>
              <div class="stat-value text-2xl font-bold blue"><span class="neon-text">{{ total_accounts }}</span></div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="box-shadow">
              <div class="stat-title">知识库总数</div>
              <div class="stat-value text-2xl font-bold green"><span class="neon-text">{{ total_data_sets }}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="box-shadow">
              <div class="stat-title">文档总数</div>
              <div class="stat-value text-2xl font-bold purple"><span class="neon-text">{{ total_documents }}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 数据趋势折线图 -->
        <el-row :gutter="20" class="mb-4">
          <el-col :span="12">
            <el-card shadow="hover">
              <div class="mb-2 font-medium">总对话量</div>
              <VChart :option="messageChartOption" autoresize style="height:260px;"/>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <div class="mb-2 font-medium">总点击量</div>
              <VChart :option="anchorChartOption" autoresize style="height:260px;"/>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mb-4">
          <el-col :span="24">
            <el-card shadow="hover">
              <div class="mb-2 font-medium">APP 对话量与点击量</div>
              <VChart :option="appBarOption" autoresize style="height:350px;"/>
            </el-card>
          </el-col>
        </el-row>
        <!-- 数据统计表格 -->
        <el-card shadow="hover">
          <div class="mb-2 font-medium">详细数据统计</div>
          <el-table :data="mergedStatistics" border size="small" style="width:100%;">
            <el-table-column prop="date" label="日期" width="120"/>
            <el-table-column prop="message_count" label="消息数" width="90"/>
            <el-table-column prop="anchor_count" label="点击量" width="90"/>
          </el-table>
        </el-card>
      </template>

      <template #footer>
        <el-button @click="showStatisticDialog = false">关闭</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="importAgentDialog" title="选择DSL文件">
      <input type="file" accept=".yml,.yaml" @change="handleFileUpload"/>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {computed, onMounted, reactive, ref} from 'vue';
import {useSidebarStore} from '../store/sidebar';
import {useRouter} from 'vue-router';
import {ElMessage, FormInstance, FormRules, UploadProps} from "element-plus";
import {createAgentApi, generatePrompt, importAgentFuc} from '@/api/agent';
import {logout, getDefaultModel, getDashboard} from '@/api/system';
import _ from "lodash"
import {
  Document,
} from "@element-plus/icons-vue";
import {useUserStore} from '@/store/user'


const userStore = useUserStore()

const message: number = 2;
const uploadUrl = `${import.meta.env.VITE_SERVE_HOST}/console/api/files/upload`;
const sidebar = useSidebarStore();
const userAvatar = computed(() => {
  return userStore.avatar !== '' ? userStore.avatar_url : '';
})
const me = computed(() => {
  return userStore.user;
})
const role = computed(() => {
  return userStore.userRole;
})
// 侧边栏折叠
const collapseChage = () => {
  sidebar.handleCollapse();
};

onMounted(() => {
  if (document.body.clientWidth < 1500) {
    collapseChage();
  }
});
const emojiList = [
  // 😀 常规表情
  '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '😍', '🥰', '😘', '😗', '😋', '😜', '🤪',

  // 💬 表达类
  '🤔', '🤨', '😐', '😑', '😶', '🙄', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢',

  // 🎉 活动类
  '🎉', '🥳', '🎂', '🎁', '🎈', '🎊', '🪅', '💌', '💯', '✔️', '✌️', '🤞', '🤟', '👍', '👎', '👏', '🙌', '🙏',

  // 🔥 热门类
  '💥', '🔥', '💦', '🌟', '💫', '✨', '💡', '🎯', '🚀', '🌈',

  // 🧠 AI / 技术
  '🤖', '🧠', '📦', '💻', '📱', '📊', '📈', '🖥️', '🧑‍💻', '🔐', '⚙️',

  // 🐶 动物
  '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🐙',

  // 🌍 地球/世界
  '🌍', '🌎', '🌏', '🌐', '🗺️', '🧭', '📍', '🚩'
];
// 用户名下拉菜单选择事件
const router = useRouter();
const handleCommand = async (command: string) => {
  if (command == 'loginout') {
    router.push('/login');
    await logout();
    localStorage.removeItem('console_token');
    localStorage.removeItem('refresh_token');
  } else if (command == 'user') {
    router.push('/ucenter');
  }
};
const setFullScreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    document.body.requestFullscreen.call(document.body);
  }
};
const createAppModel = ref(false);
const createAppModel2 = ref(false);
const description = ref('');

interface RuleForm {
  name: string
  icon_type: string
  icon: string
  iconImage: string
  icon_background: string
  mode: string
  description: string
}

const rules = reactive<FormRules<RuleForm>>({
  name: [
    {required: true, message: '为应用指定一个唯一的名称', trigger: 'blur'},
    {min: 3, max: 40, message: '长度在3-40个字符', trigger: 'blur'},
  ],
  description: [
    {required: false, message: '介绍应用功能并向应用用户展示', trigger: 'blur'},
  ],
  icon: [
    {required: true, message: '请选择一个表情', trigger: 'blur'},
  ],
});
const ruleForm = reactive<RuleForm>({
  name: '',
  icon_type: 'emoji',
  icon: '🤖',
  iconImage: '',
  icon_background: '#FFEAD5',
  mode: 'chat',
  description: '',
});

const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  if (response) {
    console.log('response', response)
    ruleForm.icon = response.id;
    ruleForm.icon_background = '#FFEAD5';
    ruleForm.icon_type = 'image';
    ruleForm.mode = 'chat';
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error('图片上传失败');
  }
};
// 上传失败处理方法
const handleUploadError = (error, uploadFile) => {
  console.error('图片上传失败:', error);
  ElMessage.error('图片上传失败，请检查网络或服务器状态');
};
const beforeUpload = (file) => {
  console.log('上传文件类型:', file.type);
  console.log('上传文件大小:', file.size);
  const allowedTypes = ['image/jpeg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB');
    return false;
  }
  console.log('eee', URL.createObjectURL(file))
  ruleForm.iconImage = URL.createObjectURL(file);
  return true;
};
const getUploadHeaders = () => {
  const token = localStorage.getItem('console_token');
  if (!token) {
    ElMessage.error('未找到有效的 token，请重新登录');
    return null;
  }
  return {
    'Authorization': `Bearer ${token}`,
    // 'X-Requested-With': 'XMLHttpRequest',
    'Accept': 'application/json'
  };
};
const uploadHeaders = ref(getUploadHeaders());
const ruleFormRef = ref<FormInstance>();
const toCreateAgent = async (formEl: FormInstance | undefined) => {
  console.log('formEl', formEl)
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      let app = {};
      if (ruleForm.icon_type == 'emoji') {
        app = {
          name: ruleForm.name,
          icon_type: "emoji",
          icon: ruleForm.icon,
          icon_background: "#FFEAD5",
          mode: ruleForm.mode,
          description: ruleForm.description
        };
      } else {
        if (ruleForm.icon_type == 'image' && !ruleForm.iconImage) {
          ruleForm.icon_type = 'emoji';
          return;
        }
        app = {
          name: ruleForm.name,
          icon_type: "image",
          icon: ruleForm.icon,
          mode: ruleForm.mode,
          description: ruleForm.description
        };
      }
      createAgentApi(app).then(res => {
        createAppModel.value = false;
        console.log('resres', res)
        if (res.mode === 'workflow' || res.mode === 'advanced-chat') {
          router.push('/flowSetting?id=' + res.id);
        } else {
          router.push('/botSetting?id=' + res.id);
        }
      });
    } else {
      console.log('error submit!', fields)
    }
  })
}

interface TemplateMeta {
  value: string
  label: string
  icon: any
  desc: string
}

const basicTemplates: TemplateMeta[] = [
  {
    value: 'chat',
    label: '聊天助手',
    icon: 'chat.png',
    desc: '简单配置即可构建基于 LLM 的对话机器人',
  },
  {
    value: 'agent-chat',
    label: 'Agent',
    icon: 'anlysis.png',
    desc: '具备推理与自主工具调用的智能助手',
  },
  {
    value: 'completion',
    label: '文本生成',
    icon: 'wordcreate.png',
    desc: '用于文本生成任务的 AI 助手',
  },
]

const advancedTemplates: TemplateMeta[] = [
  {
    value: 'advanced-chat',
    label: 'Chatflow',
    icon: 'memory.png',
    desc: '支持记忆的复杂多轮会话工作流',
  },
  {
    value: 'workflow',
    label: '工作流',
    icon: 'flow.png',
    desc: '面向链式自动化任务的编排工作流',
  },
]

const form = reactive({
  type: basicTemplates[0].value,
  name: '',
  icon: '',
  desc: '',
})

function selectType(value: string) {
  // if(value === 'advanced-chat' || value === 'workflow' || value === 'agent-chat'){
  //   ElMessage.warning('即将上线，敬请期待~');
  //   return;
  // }
  ruleForm.mode = value
}

const showStatisticDialog = ref(false);


const showStatistic = async () => {
  showStatisticDialog.value = true;
  await fetchMonitorStats();
}
// 统计弹窗相关数据
const monitorPreset = ref("all")
const monitorPresets = [
  {label: "近7天", value: "7d"},
  {label: "近30天", value: "30d"},
  {label: "本月", value: "month"},
  {label: "全部", value: "all"}
]
const monitorRange = ref<[Date, Date] | null>(null)

const total_apps = ref(0)
const total_accounts = ref(0)
const total_data_sets = ref(0)
const total_documents = ref(0)

const message_statistics = ref<{ date: string, count: number }[]>([])
const anchor_statistics = ref<{ date: string, count: number }[]>([])

// 合并统计表数据
const mergedStatistics = ref<{ date: string, message_count: number, anchor_count: number }[]>([])

// 折线图option
const messageChartOption = ref({})
const anchorChartOption = ref({})
const appBarOption = ref({})
// 区间快捷切换
const applyMonitorPreset = () => {
  const today = new Date()
  let start: Date, end: Date
  switch (monitorPreset.value) {
    case "7d":
      start = new Date(today)
      start.setDate(today.getDate() - 6)
      end = today
      break
    case "30d":
      start = new Date(today)
      start.setDate(today.getDate() - 29)
      end = today
      break
    case "month":
      start = new Date(today.getFullYear(), today.getMonth(), 1)
      end = today
      break
    default:
      monitorRange.value = null
      fetchMonitorStats()
      return
  }
  monitorRange.value = [start, end]
  fetchMonitorStats()
}

// 获取统计数据
const fetchMonitorStats = async () => {
  // 支持自定义区间
  let params: any = {}
  if (monitorRange.value) {
    params.start_date = monitorRange.value[0].toISOString().slice(0, 10)
    params.end_date = monitorRange.value[1].toISOString().slice(0, 10)
  }
  // getDashboard返回结构：见你的json
  const res = await getDashboard(params)
  total_apps.value = res.data.total_apps
  total_accounts.value = res.data.total_accounts
  total_data_sets.value = res.data.total_data_sets
  total_documents.value = res.data.total_documents
  message_statistics.value = res.data.message_statistics || []
  anchor_statistics.value = res.data.anchor_statistics || []

  // 合并消息和点击量据（按日期对齐）
  const allDates = _.uniq([
    ...(message_statistics.value.map(i => i.date)),
    ...(anchor_statistics.value.map(i => i.date)),
  ]).sort()
  mergedStatistics.value = allDates.map(date => ({
    date,
    message_count: message_statistics.value.find(i => i.date === date)?.count || 0,
    anchor_count: anchor_statistics.value.find(i => i.date === date)?.count || 0,
  }))

  const app_statistics = res.data.app_statistics || []
  const appNames = app_statistics.map(i => i.app_name)
  const messageCounts = app_statistics.map(i => i.message_count)
  const clickCounts = app_statistics.map(i => i.click_count)
  appBarOption.value = {
    tooltip: {trigger: 'axis'},
    legend: {data: ['消息数', '点击量']},
    xAxis: {
      type: 'category',
      data: appNames,
      axisLabel: {rotate: 30, interval: 0},
    },
    yAxis: {type: 'value'},
    dataZoom: [
      {type: 'slider', show: appNames.length > 12, start: 0, end: appNames.length > 12 ? 40 : 100}
    ],
    series: [
      {name: '消息数', type: 'bar', data: messageCounts, barWidth: 20},
      {name: '点击量', type: 'bar', data: clickCounts, barWidth: 20}
    ]
  }
  // 构造折线图 option
  messageChartOption.value = {
    xAxis: {type: 'category', data: mergedStatistics.value.map(i => i.date)},
    yAxis: {type: 'value'},
    series: [
      {name: '消息数', type: 'line', data: mergedStatistics.value.map(i => i.message_count)}
    ],
    tooltip: {trigger: 'axis'},
  }
  anchorChartOption.value = {
    xAxis: {type: 'category', data: mergedStatistics.value.map(i => i.date)},
    yAxis: {type: 'value'},
    series: [
      {name: '点击量', type: 'line', data: mergedStatistics.value.map(i => i.anchor_count)}
    ],
    tooltip: {trigger: 'axis'}
  }
  // 合并消息和点击量据（按日期对齐）
  const allDates2 = _.uniq([
    ...(message_statistics.value.map(i => i.date)),
    ...(anchor_statistics.value.map(i => i.date)),
  ]).sort()
  mergedStatistics.value = allDates2.map(date => ({
    date,
    message_count: message_statistics.value.find(i => i.date === date)?.count || 0,
    anchor_count: anchor_statistics.value.find(i => i.date === date)?.count || 0,
  }))
}
const importAgentDialog = ref(false);
const importAgentByDsl = () => {
  importAgentDialog.value = true;
}
const handleFileUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) {
    return;
  }

  // 用 FileReader 在浏览器里读取文件内容
  const reader = new FileReader();
  reader.onload = async (e) => {
    const fileContent = e.target.result;
    console.log(typeof fileContent);
    // 在这里就可以直接调用后端接口，传文件的文本内容
    try {
      await importAgentFuc({
        mode: "yaml-content",
        yaml_content: fileContent
      });
      importAgentDialog.value = false;
      createAppModel.value = false;
      location.reload();
      console.log('上传成功');
    } catch (err) {
      console.error('上传失败', err);
    }
  };

  reader.readAsText(file, 'utf-8');
};

</script>
<style scoped lang="less">
.header {
  position: absolute;
  top: 0;
  left: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  width: 72px;
  margin: 10px 0;
  height: 98%;
  color: var(--header-text-color);
  background-color: var(--header-bg-color);
  padding: 16px 6px;
  border: 1px solid rgba(82, 100, 154, 0.13);
  border-right: none;
  border-top-left-radius: 14px;
  border-bottom-left-radius: 14px;
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 35px;
}

.web-title {
  margin: 0 40px 0 10px;
  font-size: 22px;
}

.collapse-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0 10px;
  cursor: pointer;
  opacity: 0.8;
  font-size: 22px;
  margin-top: 20px;
}

.collapse-btn:hover {
  opacity: 1;
}

.header-right {
  float: right;
  display: flex;
  flex-direction: column;
}

.header-user-con {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.btn-fullscreen {
  transform: rotate(45deg);
  font-size: 24px;
}

.btn-icon {
  display: flex;
  justify-content: center;
  position: relative;
  width: 25px;
  height: 30px;
  text-align: center;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: var(--header-text-color);
  margin: 8px 0;
  font-size: 20px;
}

.btn-bell-badge {
  position: absolute;
  right: 4px;
  top: 0px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #f56c6c;
  color: var(--header-text-color);
}

.user-avator {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  margin-left: -10px;
}

.el-dropdown-link {
  color: var(--header-text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
}

.el-dropdown-menu__item {
  text-align: center;
}

.btnIcon {
  margin-top: 20px;
  font-size: 20px;
  padding: 6px 8px 0px;
  border-radius: 10px;
  cursor: pointer;

  &:hover {
    background-color: rgba(87, 104, 161, .08);
  }
}

.boxs {
  display: flex;
  justify-content: space-between;
  gap: 20px; /* 确保左右之间有间距 */

  img {
    width: 314px;
  }

  .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    border: 1px solid rgba(82, 100, 154, .08);
    border-radius: 12px;
    position: relative;

    &:hover {
      box-shadow: 0 4px 12px 0 rgba(0, 0, 0, .08), 0 8px 24px 0 rgba(0, 0, 0, .04);
    }

    > :nth-child(2) {
      margin-top: 20px;
      font-size: 20px;
      color: rgba(8, 13, 30, 0.9);
      margin-bottom: 10px; /* 确保文字和图片之间的间距合理 */
    }

    > :nth-child(3) {
      font-size: 14px;
      color: rgba(32, 41, 69, 0.62);
      opacity: 1;
      visibility: visible;
      //transition: opacity 0.3s ease, visibility 0s 0.3s;
      margin-bottom: 10px; /* 修正文字下方的位置 */
    }

    > :nth-child(4) {
      background-color: rgb(81, 71, 255);
      color: white;
      opacity: 0;
      visibility: hidden;
      position: absolute;
      bottom: 16px;
      transition: opacity 0.3s ease, visibility 0s 0.3s;
    }

    &:hover {
      > :nth-child(3) {
        opacity: 0;
        visibility: hidden;
      }

      > :nth-child(4) {
        opacity: 1;
        visibility: visible;
      }
    }
  }
}


.tabs {
  margin-bottom: 20px;
  border-radius: 10px;
  padding: 4px;
  gap: 3px;
  background-color: rgba(87, 104, 161, .08);
  display: flex;

  > div {
    padding: 4px 10px;
    flex: 1;
    text-align: center;
    border-radius: 8px;

    &:hover {
      background-color: #E3E5F2;
    }
  }

  .active {
    background-color: #fff !important;
  }
}

.lab {
  padding: 12px 0;
}

.avatar-uploader {
  width: 64px;
  height: 64px;
}

.watiUpImg {
  width: 64px;
  height: 64px;
  border-radius: 14px;
}

.line {
  margin-top: 10px;
  width: 80%;
  border-bottom: 1px solid rgba(56, 55, 67, 0.08);
}

.emoji-grid {
  span {
    font-size: 20px;
    padding: 5px;
  }
}

.icon_type {
  margin-bottom: 20px;
}

.showIcon {
  padding: 20px 0 27px;
  border-radius: 10px;
  font-size: 60px;
  background-color: #FFEAD5;
}

.create-app-form {
  max-height: 80vh;
  overflow-y: auto;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}

.group-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
  margin-top: 8px;
}

.template-card {
  transition: border-color 0.2s, box-shadow 0.2s;
  border: 1px solid var(--el-border-color);
  border-radius: 12px;
  height: 100%;
}

.template-card:hover {
  border-color: var(--el-color-primary);
}

.template-card.active {
  border: 2px solid var(--el-color-primary);
}

.avatar {
  background: var(--el-color-info-light-8);
  color: var(--el-color-info);
  width: 20px;
}

.desc {
  font-size: 12px;
  line-height: 16px;
  color: var(--el-text-color-secondary);
  margin: 2px 0 0;
}

.icon-picker {
  width: 64px;
  height: 64px;
}

.stat-value {
  font-size: 30px;
}

</style>
